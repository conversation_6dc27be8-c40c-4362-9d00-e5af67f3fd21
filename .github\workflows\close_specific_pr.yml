name: Close Specific PR

on:
  pull_request_target:
    types: [opened, ]
    # Note: If you use both the branches filter and the paths filter, the workflow will only run when both filters are satisfied.
    branches:
      - master
    paths:
      - 'usage/workflow_download.py'
jobs:
  close_specific_pr:
    env:
      comment: |
          To prevent beginners from mistakenly submitting PRs, 
          if your PR only modifies the usage/workflow_download.py file, 
          it will be automatically closed. 
          If you really want to submit a PR, please reopen it yourself. 
          Make sure you know what you are doing!
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Close PR
        run: |
          PR_TITLE=$(jq -r ".pull_request.title" $GITHUB_EVENT_PATH)
          echo "PR_TITLE: $PR_TITLE"
          gh pr comment ${{ github.event.pull_request.number }} --repo ${{ github.repository }} --body '${{ env.comment }}'
          gh pr close ${{ github.event.pull_request.number }} --repo ${{ github.repository }}
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}