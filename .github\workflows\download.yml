name: 下载JM本子 (commit)

on:
  workflow_dispatch:
  push:
    paths:
      - '.github/workflows/download.yml' # 工作流定义
      - 'usage/workflow_download.py' # 下载脚本
      - 'assets/option/option_workflow_download.yml' # 配置文件

jobs:
  crawler:
    runs-on: ubuntu-latest
    env:
      # 登录相关secrets
      JM_USERNAME: ${{ secrets.JM_USERNAME }}
      JM_PASSWORD: ${{ secrets.JM_PASSWORD }}

      # 邮件相关secrets
      EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
      EMAIL_TO: ${{ secrets.EMAIL_TO }}
      EMAIL_PASS: ${{ secrets.EMAIL_PASS }}
      EMAIL_TITLE: ${{ secrets.EMAIL_TITLE }}
      EMAIL_CONTENT: ${{ secrets.EMAIL_CONTENT }}
      
      # 固定值
      JM_DOWNLOAD_DIR: /home/<USER>/work/jmcomic/download/
      ZIP_NAME: '本子.tar.gz'
      UPLOAD_NAME: 'Click me to download'

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install Dependency
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: 安装jmcomic（pip）
        if: ${{ github.ref != 'refs/heads/dev' }}
        run: |
          pip install jmcomic -i https://pypi.org/project --upgrade

      - name: 安装jmcomic（local）
        if: ${{ github.ref == 'refs/heads/dev' }}
        run: |
          pip install -e ./

      - name: 运行下载脚本
        run: |
          cd ./usage/
          python workflow_download.py

      - name: 压缩文件
        run: |
          cd $JM_DOWNLOAD_DIR
          tar -zcvf "../$ZIP_NAME" ./
          mv "../$ZIP_NAME" .

      - name: 上传结果
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.UPLOAD_NAME }}
          path: ${{ env.JM_DOWNLOAD_DIR }}/${{ env.ZIP_NAME }}
          if-no-files-found: warn
          retention-days: 90
