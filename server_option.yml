# 开启jmcomic的日志输出，默认为true
log: true

# 配置客户端相关
client:
  # impl: 客户端实现类
  impl: html

  # domain: 禁漫域名配置
  domain:
    html:
      - 18comic.vip
      - jmcomic-zzz.org
      - 18comic.org
    api:
      - www.jmapiproxyxxx.vip

  # retry_times: 请求失败重试次数，默认为5
  retry_times: 5

  # postman: 请求配置
  postman:
    meta_data:
      # proxies: 代理配置，默认是 system，表示使用系统代理
      proxies: system

      # cookies: 帐号配置，默认是 null，表示未登录状态访问JM
      cookies:
        AVS: qkwehjjasdowqeq # 这个值是乱打的，不能用

# 下载配置
download:
  cache: true # 如果要下载的文件在磁盘上已存在，不用再下一遍了吧？
  image:
    decode: true # JM的原图是混淆过的，要不要还原？
    suffix: null # 把图片都转为.jpg格式，默认为null，表示不转换
  threading:
    # image: 同时下载的图片数，默认是30张图
    image: 30

# 文件夹规则配置，决定图片文件存放在服务器上的哪个文件夹
dir_rule:
  # base_dir: 根目录（服务器相对路径）
  base_dir: ./downloads/

  # rule: 规则dsl
  # 默认规则是: 根目录 / 章节标题 / 图片文件
  rule: Bd_Ptitle
