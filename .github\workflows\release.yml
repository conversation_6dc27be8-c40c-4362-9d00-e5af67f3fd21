# publish when new release
name: Publish If Release

on:
  workflow_dispatch:
  release:
    types: [ published ]

jobs:
  release:
    name: Publish `jmcomic` to PYPI
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Build
        run: |
          python -m pip install build
          python -m build

      - name: Publish PYPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_JMCOMIC }}
