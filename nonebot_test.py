from nonebot.rule import to_me
from nonebot.plugin import on_command
import httpx
import asyncio
from nonebot.adapters import Message
from nonebot.params import CommandArg

# 配置API服务器地址
API_BASE_URL = "http://localhost:8000"  # 根据实际部署地址修改

# 注册命令处理器
status = on_command("当前状态", rule=to_me(), aliases={"111", "status"}, priority=10, block=True)
download = on_command("下载", rule=to_me(), aliases={"download"}, priority=10, block=True)
files = on_command("文件列表", rule=to_me(), aliases={"files", "list"}, priority=10, block=True)

@status.handle()
async def handle_status():
    """调用查询服务状态接口"""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{API_BASE_URL}/status")
            
            if response.status_code == 200:
                data = response.json()
                message = f"""📊 JMComic API 服务状态
🟢 状态: {data.get('status', 'unknown')}
📁 下载目录: {data.get('download_dir', 'unknown')}
⚙️ 配置文件: {data.get('config_file', 'unknown')}
🔗 静态文件前缀: {data.get('static_url_prefix', 'unknown')}"""
            else:
                message = f"❌ 服务状态查询失败，HTTP状态码: {response.status_code}"
                
    except httpx.TimeoutException:
        message = "⏰ 请求超时，服务可能未启动或网络异常"
    except httpx.ConnectError:
        message = "🔌 连接失败，请检查服务是否启动"
    except Exception as e:
        message = f"❌ 查询失败: {str(e)}"
    
    await status.finish(message)


@download.handle()
async def handle_download(args: Message = CommandArg()):
    """调用下载接口"""
    album_id = args.extract_plain_text().strip()
    
    if not album_id:
        await download.finish("❌ 请提供要下载的本子ID，例如: 下载 524153")
        return
    
    try:
        await download.send(f"🚀 开始下载本子: {album_id}，请稍候...")
        
        async with httpx.AsyncClient(timeout=300.0) as client:  # 下载可能需要较长时间
            response = await client.post(
                f"{API_BASE_URL}/download",
                params={"album_id": album_id}
            )
            
            if response.status_code == 200:
                data = response.json()
                files_count = len(data.get('files', []))
                title = data.get('title', f'Album_{album_id}')
                
                message = f"""✅ 下载完成！
📖 标题: {title}
🆔 ID: {album_id}
📁 文件数量: {files_count}
💾 存储路径: {data.get('download_path', 'unknown')}

使用 "文件列表" 命令查看所有下载的文件"""
            else:
                error_detail = response.json().get('detail', '未知错误') if response.headers.get('content-type', '').startswith('application/json') else response.text
                message = f"❌ 下载失败，HTTP状态码: {response.status_code}\n错误详情: {error_detail}"
                
    except httpx.TimeoutException:
        message = "⏰ 下载超时，请稍后重试"
    except httpx.ConnectError:
        message = "🔌 连接失败，请检查服务是否启动"
    except Exception as e:
        message = f"❌ 下载失败: {str(e)}"
    
    await download.finish(message)


@files.handle()
async def handle_files():
    """调用文件列表接口"""
    message = ""  # 初始化message变量

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{API_BASE_URL}/files/list")

            if response.status_code == 200:
                data = response.json()
                file_list_data = data.get('files', [])
                total = data.get('total', 0)

                if total == 0:
                    message = "📂 暂无下载文件"
                else:
                    # 限制显示文件数量，避免消息过长
                    display_limit = 10
                    file_display_list = []

                    for i, file_info in enumerate(file_list_data[:display_limit]):
                        filename = file_info.get('filename', 'unknown')
                        size_mb = round(file_info.get('size', 0) / 1024 / 1024, 2)
                        file_display_list.append(f"{i+1}. {filename} ({size_mb}MB)")

                    files_text = '\n'.join(file_display_list)
                    more_text = f"\n... 还有 {total - display_limit} 个文件" if total > display_limit else ""

                    message = f"""📂 文件列表 (共{total}个文件)
{files_text}{more_text}

💡 文件访问地址格式: {API_BASE_URL}/files/文件路径"""
            else:
                message = f"❌ 获取文件列表失败，HTTP状态码: {response.status_code}"

    except httpx.TimeoutException:
        message = "⏰ 请求超时"
    except httpx.ConnectError:
        message = "🔌 连接失败，请检查服务是否启动"
    except Exception as e:
        message = f"❌ 获取文件列表失败: {str(e)}"

    await files.finish(message)


# 根路径测试命令
root_test = on_command("api测试", rule=to_me(), aliases={"test", "ping"}, priority=10, block=True)

@root_test.handle()
async def handle_root_test():
    """测试API根路径"""
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{API_BASE_URL}/")
            
            if response.status_code == 200:
                data = response.json()
                message = f"""🎯 API连接测试成功
📝 消息: {data.get('message', 'unknown')}
🔢 版本: {data.get('version', 'unknown')}
👤 作者: {data.get('author', 'unknown')}"""
            else:
                message = f"❌ API测试失败，HTTP状态码: {response.status_code}"
                
    except httpx.TimeoutException:
        message = "⏰ 请求超时，服务可能未启动"
    except httpx.ConnectError:
        message = "🔌 连接失败，请检查服务地址和端口"
    except Exception as e:
        message = f"❌ API测试失败: {str(e)}"
    
    await root_test.finish(message)
