FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖和curl（用于健康检查）
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目源码
COPY src/ ./src/
COPY setup.py .
COPY pyproject.toml .
COPY README.md .
COPY LICENSE .

# 安装jmcomic库
RUN pip install -e .

# 复制API文件和配置
COPY web_api.py .
COPY server_option.yml .

# 创建下载目录并设置权限
RUN mkdir -p /app/downloads && chmod 755 /app/downloads

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/status || exit 1

# 启动命令
CMD ["uvicorn", "web_api:app", "--host", "0.0.0.0", "--port", "8000"]
