# auto release and publish
name: Auto Release & Publish

on:
  workflow_dispatch:
  push:
    branches:
      - master

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    if: startsWith(github.event.head_commit.message, 'v')
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Parse Tag & Body
        id: tb
        run: |
          commit_message=$(git log --format=%B -n 1 ${{ github.sha }})
          python .github/release.py "$commit_message"

      - name: Create Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.tb.outputs.tag }}
          body_path: release_body.txt

      - name: Build
        run: |
          python -m pip install build
          python -m build

      - name: Release PYPI
        uses: pypa/gh-action-pypi-publish@release/v1
#        with:
#          password: ${{ secrets.PYPI_JMCOMIC }}
