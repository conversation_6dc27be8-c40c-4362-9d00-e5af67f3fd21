from fastapi import FastAP<PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import jmcomic
import os
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="JMComic Download API", version="1.0.0")

# 配置文件路径和下载目录
CONFIG_FILE = "./server_option.yml"
DOWNLOAD_BASE_DIR = "./downloads"  # 服务器下载目录
STATIC_URL_PREFIX = "/files"  # 静态文件URL前缀

# 确保下载目录存在
os.makedirs(DOWNLOAD_BASE_DIR, exist_ok=True)

# 挂载静态文件服务
app.mount(STATIC_URL_PREFIX, StaticFiles(directory=DOWNLOAD_BASE_DIR), name="files")

# 线程池执行器
executor = ThreadPoolExecutor(max_workers=2)


def download_album_sync(album_id: str, config_path: str) -> dict:
    """同步下载函数"""
    try:
        # 创建配置对象
        option = jmcomic.create_option_by_file(config_path)
        
        # 修改下载目录为服务器目录
        option.dir_rule.base_dir = DOWNLOAD_BASE_DIR
        
        # 下载本子
        album, downloader = jmcomic.download_album(album_id, option)
        
        # 获取下载的文件信息
        download_info = {
            "album_id": album_id,
            "title": album.title if hasattr(album, 'title') else f"Album_{album_id}",
            "status": "success",
            "download_path": DOWNLOAD_BASE_DIR,
            "files": []
        }
        
        # 扫描下载目录获取文件列表
        album_dir = os.path.join(DOWNLOAD_BASE_DIR)
        if os.path.exists(album_dir):
            for root, dirs, files in os.walk(album_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, DOWNLOAD_BASE_DIR)
                    file_url = f"{STATIC_URL_PREFIX}/{relative_path.replace(os.sep, '/')}"
                    download_info["files"].append({
                        "filename": file,
                        "path": relative_path,
                        "url": file_url
                    })
        
        return download_info
        
    except Exception as e:
        logger.error(f"下载失败: {str(e)}")
        raise Exception(f"下载失败: {str(e)}")


@app.get("/")
async def root():
    """根路径"""
    return {"message": "JMComic Download API", "version": "1.0.0","author":"Z1p"}


@app.post("/download")
async def download_album_api(album_id: str, config_file: Optional[str] = None):
    """
    下载本子API
    
    Args:
        album_id: 本子ID
        config_file: 配置文件路径（可选，默认使用内置配置）
    
    Returns:
        下载结果和文件URL列表
    """
    try:
        # 验证album_id
        if not album_id or not album_id.strip():
            raise HTTPException(status_code=400, detail="album_id不能为空")
        
        # 使用默认配置文件或指定的配置文件
        config_path = config_file if config_file else CONFIG_FILE
        
        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            raise HTTPException(status_code=400, detail=f"配置文件不存在: {config_path}")
        
        logger.info(f"开始下载本子: {album_id}")
        
        # 异步执行下载任务
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            executor, 
            download_album_sync, 
            album_id, 
            config_path
        )
        
        logger.info(f"下载完成: {album_id}")
        return JSONResponse(content=result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/files/list")
async def list_files():
    """
    列出所有下载的文件
    
    Returns:
        文件列表
    """
    try:
        files = []
        if os.path.exists(DOWNLOAD_BASE_DIR):
            for root, dirs, filenames in os.walk(DOWNLOAD_BASE_DIR):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, DOWNLOAD_BASE_DIR)
                    file_url = f"{STATIC_URL_PREFIX}/{relative_path.replace(os.sep, '/')}"
                    files.append({
                        "filename": filename,
                        "path": relative_path,
                        "url": file_url,
                        "size": os.path.getsize(file_path)
                    })
        
        return {"files": files, "total": len(files)}
        
    except Exception as e:
        logger.error(f"列出文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/status")
async def get_status():
    """
    获取服务状态
    
    Returns:
        服务状态信息
    """
    return {
        "status": "running",
        "download_dir": DOWNLOAD_BASE_DIR,
        "config_file": CONFIG_FILE,
        "static_url_prefix": STATIC_URL_PREFIX
    }


# 如果直接运行此文件，启动服务器
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
