name: 下载JM本子 (dispatch)

on:
  workflow_dispatch:
    inputs:
      JM_ALBUM_IDS:
        type: string
        description: 本子id（多个id用-隔开，如 '123-456-789'）
        required: false

      JM_PHOTO_IDS:
        type: string
        description: 章节id（单独下载章节，多个id同上）
        required: false

      CLIENT_IMPL:
        type: string
        description: 客户端类型（client.impl），下载失败时，你可以尝试填入此项重试。'api' 表示移动端，'html' 表示网页端。
        default: ''
        required: false

      IMAGE_SUFFIX:
        type: string
        description: 图片后缀（download.cache.suffix），默认为空，表示不做图片格式转换。可填入例如 'png' 'jpg'
        default: ''
        required: false

      DIR_RULE:
        type: string
        description: 下载文件夹规则（dir_rule.rule）。默认使用配置文件的 'Bd_Aauthor_Atitle_Pindex'。
        default: ''
        required: false

      ZIP_NAME:
        type: string
        default: 本子.tar.gz
        description: 压缩文件名称
        required: false

      UPLOAD_NAME:
        type: string
        default: Click me to download
        description: 上传文件名称
        required: false

#      JM_USERNAME:
#        type: string
#        default: ''
#        description: '禁漫帐号（不建议使用，用户名和密码会泄露在日志中。最好用secrets）'
#        required: false
#
#      JM_PASSWORD:
#        type: string
#        default: ''
#        description: '禁漫密码（不建议使用，用户名和密码会泄露在日志中。最好用secrets）'
#        required: false


jobs:
  crawler:
    runs-on: ubuntu-latest
    env:
      # 工作流输入
      JM_ALBUM_IDS: ${{ github.event.inputs.JM_ALBUM_IDS }}
      JM_PHOTO_IDS: ${{ github.event.inputs.JM_PHOTO_IDS }}
      DIR_RULE: ${{ github.event.inputs.DIR_RULE }}
      CLIENT_IMPL: ${{ github.event.inputs.CLIENT_IMPL }}
      ZIP_NAME: ${{ github.event.inputs.ZIP_NAME }}
      UPLOAD_NAME: ${{ github.event.inputs.UPLOAD_NAME }}
      IMAGE_SUFFIX: ${{ github.event.inputs.IMAGE_SUFFIX }}

      # 登录相关secrets
      JM_USERNAME: ${{ secrets.JM_USERNAME }}
      JM_PASSWORD: ${{ secrets.JM_PASSWORD }}

      # 邮件相关secrets
      EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
      EMAIL_TO: ${{ secrets.EMAIL_TO }}
      EMAIL_PASS: ${{ secrets.EMAIL_PASS }}
      EMAIL_TITLE: ${{ secrets.EMAIL_TITLE }}
      EMAIL_CONTENT: ${{ secrets.EMAIL_CONTENT }}

      # 固定值
      JM_DOWNLOAD_DIR: /home/<USER>/work/jmcomic/download/

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install Dependency
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt

      - name: 安装jmcomic（pip）
        if: ${{ github.ref != 'refs/heads/dev' }}
        run: |
          pip install jmcomic -i https://pypi.org/project --upgrade

      - name: 安装jmcomic（local）
        if: ${{ github.ref == 'refs/heads/dev' }}
        run: |
          pip install -e ./

      - name: 运行下载脚本
        run: |
          cd ./usage/
          python workflow_download.py

      - name: 压缩文件
        run: |
          cd $JM_DOWNLOAD_DIR
          tar -zcvf "../$ZIP_NAME" ./
          mv "../$ZIP_NAME" .

      - name: 上传结果
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.UPLOAD_NAME }}
          path: ${{ env.JM_DOWNLOAD_DIR }}/${{ env.ZIP_NAME }}
          if-no-files-found: error
          retention-days: 90
